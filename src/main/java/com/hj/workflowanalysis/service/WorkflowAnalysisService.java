package com.hj.workflowanalysis.service;

import com.hj.workflowanalysis.entity.WorkflowRequestflowinfo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import jakarta.servlet.http.HttpServletResponse;

public interface WorkflowAnalysisService {

    void analysis();

    /**
     * 导出Excel格式的工作流分析报告
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param response HTTP响应
     */
    void exportExcel(String startDate, String endDate, HttpServletResponse response);

    /**
     * 按二级部门分别导出Excel并发送邮件给部门经理
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     */
    void exportAndSendEmailByDepartment(String startDate, String endDate);

}
