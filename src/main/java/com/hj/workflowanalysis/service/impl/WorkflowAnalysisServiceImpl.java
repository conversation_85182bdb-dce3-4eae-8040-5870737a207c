package com.hj.workflowanalysis.service.impl;

import com.hj.workflowanalysis.dao.FormtableMain413Dao;
import com.hj.workflowanalysis.dao.HrmdepartmentDao;
import com.hj.workflowanalysis.dao.HrmresourceDao;
import com.hj.workflowanalysis.dao.WorkflowRequestflowinfoDao;
import com.hj.workflowanalysis.entity.FormtableMain413;
import com.hj.workflowanalysis.entity.Hrmdepartment;
import com.hj.workflowanalysis.entity.Hrmresource;
import com.hj.workflowanalysis.entity.WorkflowRequestflowinfo;
import com.hj.workflowanalysis.service.WorkflowAnalysisService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;


@Service("workflowAnalysisService")
public class WorkflowAnalysisServiceImpl implements WorkflowAnalysisService {

    @Resource
    private WorkflowRequestflowinfoDao workflowRequestflowinfoDao;

    @Resource
    private FormtableMain413Dao formtableMain413Dao;

    @Resource
    private HrmresourceDao hrmresourceDao;

    @Resource
    private HrmdepartmentDao hrmdepartmentDao;

    @Override
    public void analysis() {
        // 1. 获取工作流数据
        List<FormtableMain413> formtableMain413List = formtableMain413Dao.queryAllByLimit("2025-05-01", "2025-05-31");
        List<Integer> requestIds = formtableMain413List.stream().map(FormtableMain413::getRequestid).toList();
        if (CollectionUtils.isEmpty(requestIds)) {
            return;
        }

        List<WorkflowRequestflowinfo> workflowRequestflowinfoList = workflowRequestflowinfoDao.queryAllByLimit(requestIds);
        if (CollectionUtils.isEmpty(workflowRequestflowinfoList)) {
            return;
        }

        // 2. 获取所有操作员ID
        List<Integer> operatorIds = workflowRequestflowinfoList.stream()
                .map(WorkflowRequestflowinfo::getOperator)
                .filter(Objects::nonNull)
                .distinct()
                .toList();

        if (CollectionUtils.isEmpty(operatorIds)) {
            return;
        }

        // 3. 查询员工信息
        List<Hrmresource> hrmresourceList = hrmresourceDao.findByResourceIds(operatorIds);
        Map<Integer, Hrmresource> resourceMap = hrmresourceList.stream()
                .collect(Collectors.toMap(Hrmresource::getId, resource -> resource));

        // 4. 查询所有部门信息
        List<Hrmdepartment> allDepartments = hrmdepartmentDao.queryAll();
        Map<Integer, Hrmdepartment> departmentMap = allDepartments.stream()
                .collect(Collectors.toMap(Hrmdepartment::getId, dept -> dept));

        // 5. 第一步分组：根据员工的departmentId分组
        Map<Integer, List<WorkflowRequestflowinfo>> firstLevelGrouping = workflowRequestflowinfoList.stream()
                .filter(workflow -> workflow.getOperator() != null)
                .filter(workflow -> resourceMap.containsKey(workflow.getOperator()))
                .collect(Collectors.groupingBy(workflow -> {
                    Hrmresource resource = resourceMap.get(workflow.getOperator());
                    return resource.getDepartmentid();
                }));

        // 6. 第二步分组：根据二级部门分组
        Map<Integer, List<WorkflowRequestflowinfo>> secondLevelGrouping = new HashMap<>();

        for (Map.Entry<Integer, List<WorkflowRequestflowinfo>> entry : firstLevelGrouping.entrySet()) {
            Integer departmentId = entry.getKey();
            List<WorkflowRequestflowinfo> workflows = entry.getValue();

            // 找到该部门对应的二级部门
            Integer secondLevelDeptId = findSecondLevelDepartment(departmentId, departmentMap);

            secondLevelGrouping.computeIfAbsent(secondLevelDeptId, k -> new ArrayList<>()).addAll(workflows);
        }

        // 7. 输出结果
        printAnalysisResult(secondLevelGrouping, departmentMap);

    }

    /**
     * 找到指定部门的二级部门ID
     * 二级部门是指从根部门（supDepId为null或0）开始数的第二层部门
     *
     * @param departmentId 部门ID
     * @param departmentMap 部门映射
     * @return 二级部门ID
     */
    private Integer findSecondLevelDepartment(Integer departmentId, Map<Integer, Hrmdepartment> departmentMap) {
        if (departmentId == null || !departmentMap.containsKey(departmentId)) {
            return departmentId; // 如果找不到部门，返回原部门ID
        }

        // 构建从当前部门到根部门的完整路径
        List<Integer> pathToRoot = new ArrayList<>();
        Integer currentId = departmentId;
        Set<Integer> visited = new HashSet<>(); // 防止循环引用

        while (currentId != null && departmentMap.containsKey(currentId) && !visited.contains(currentId)) {
            visited.add(currentId);
            pathToRoot.add(currentId);
            Hrmdepartment dept = departmentMap.get(currentId);
            currentId = dept.getSupdepid();

            // 如果supDepId为0，也视为根部门
            if (currentId != null && currentId == 0) {
                currentId = null;
            }
        }

        // 反转路径，使其从根部门开始
        Collections.reverse(pathToRoot);

        // 如果路径长度小于2，说明没有二级部门，返回当前部门
        if (pathToRoot.size() < 2) {
            return departmentId;
        }

        // 返回第二级部门（索引为1）
        return pathToRoot.get(1);
    }

    /**
     * 打印分析结果
     */
    private void printAnalysisResult(Map<Integer, List<WorkflowRequestflowinfo>> grouping,
                                   Map<Integer, Hrmdepartment> departmentMap) {
        System.out.println("=== 工作流分析结果（按二级部门分组） ===");
        System.out.println("说明：二级部门是指从根部门开始数的第二层部门");
        System.out.println();

        for (Map.Entry<Integer, List<WorkflowRequestflowinfo>> entry : grouping.entrySet()) {
            Integer deptId = entry.getKey();
            List<WorkflowRequestflowinfo> workflows = entry.getValue();

            String deptName = departmentMap.containsKey(deptId) ?
                departmentMap.get(deptId).getDepartmentname() : "未知部门";

            // 显示部门层级路径
            String deptPath = getDepartmentPath(deptId, departmentMap);

            System.out.printf("二级部门: %s (ID: %d)%n", deptName, deptId);
            System.out.printf("部门路径: %s%n", deptPath);
            System.out.printf("工作流数量: %d%n", workflows.size());

            // 可以根据需要添加更详细的统计信息
            workflows.forEach(workflow -> System.out.printf("  - RequestID: %d, Operator: %d, NodeID: %d%n",
                workflow.getRequestid(), workflow.getOperator(), workflow.getNodeid()));
            System.out.println("----------------------------------------");
        }
    }

    /**
     * 获取部门的完整路径
     */
    private String getDepartmentPath(Integer departmentId, Map<Integer, Hrmdepartment> departmentMap) {
        if (departmentId == null || !departmentMap.containsKey(departmentId)) {
            return "未知路径";
        }

        List<String> pathNames = new ArrayList<>();
        Integer currentId = departmentId;
        Set<Integer> visited = new HashSet<>();

        while (currentId != null && departmentMap.containsKey(currentId) && !visited.contains(currentId)) {
            visited.add(currentId);
            Hrmdepartment dept = departmentMap.get(currentId);
            pathNames.add(dept.getDepartmentname());
            currentId = dept.getSupdepid();

            if (currentId != null && currentId == 0) {
                currentId = null;
            }
        }

        Collections.reverse(pathNames);
        return String.join(" > ", pathNames);
    }

    @Override
    public void exportExcel(String startDate, String endDate, HttpServletResponse response) {
        try {
            // 1. 获取数据
            List<FormtableMain413> formtableMain413List = formtableMain413Dao.queryAllByLimit(startDate, endDate);
            if (CollectionUtils.isEmpty(formtableMain413List)) {
                return;
            }

            // 2. 获取所有员工信息
            List<Integer> employeeIds = formtableMain413List.stream()
                    .map(FormtableMain413::getBzcxm)
                    .filter(Objects::nonNull)
                    .distinct()
                    .toList();

            Map<Integer, Hrmresource> employeeMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(employeeIds)) {
                List<Hrmresource> employees = hrmresourceDao.findByResourceIds(employeeIds);
                employeeMap = employees.stream()
                        .collect(Collectors.toMap(Hrmresource::getId, emp -> emp));
            }

            // 3. 获取所有部门信息
            List<Hrmdepartment> allDepartments = hrmdepartmentDao.queryAll();
            Map<Integer, Hrmdepartment> departmentMap = allDepartments.stream()
                    .collect(Collectors.toMap(Hrmdepartment::getId, dept -> dept));

            // 4. 按部门和员工分组数据
            Map<String, Map<String, Map<String, String>>> groupedData = groupDataForExcel(
                    formtableMain413List, employeeMap, departmentMap);

            // 5. 生成日期列表
            List<String> dateList = generateDateList(startDate, endDate);

            // 6. 创建Excel文件
            createExcelFile(groupedData, dateList, response);

        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("导出Excel失败", e);
        }
    }

    /**
     * 按部门和员工分组数据
     */
    private Map<String, Map<String, Map<String, String>>> groupDataForExcel(
            List<FormtableMain413> dataList,
            Map<Integer, Hrmresource> employeeMap,
            Map<Integer, Hrmdepartment> departmentMap) {

        Map<String, Map<String, Map<String, String>>> result = new LinkedHashMap<>();

        for (FormtableMain413 data : dataList) {
            // 获取员工信息
            Hrmresource employee = employeeMap.get(data.getBzcxm());
            if (employee == null) {
                continue;
            }

            // 获取部门信息
            Hrmdepartment department = departmentMap.get(employee.getDepartmentid());
            String departmentName = department != null ? department.getDepartmentname() : "未知部门";

            // 获取员工姓名
            String employeeName = employee.getLastname();

            // 获取班次类型
            String shiftType = getShiftTypeName(data.getBclx());

            // 分组数据
            result.computeIfAbsent(departmentName, k -> new LinkedHashMap<>())
                    .computeIfAbsent(employeeName, k -> new LinkedHashMap<>())
                    .put(data.getRq(), shiftType);
        }

        return result;
    }

    /**
     * 获取班次类型名称
     */
    private String getShiftTypeName(Integer bclx) {
        if (bclx == null) {
            return "";
        }
        // 根据实际业务逻辑映射班次类型
        // 这里需要根据您的实际数据来调整
        switch (bclx) {
            case 1:
                return "白班";
            case 2:
                return "夜班";
            case 3:
                return "公休";
            case 4:
                return "请假";
            default:
                return "其他";
        }
    }

    /**
     * 生成日期列表
     */
    private List<String> generateDateList(String startDate, String endDate) {
        List<String> dateList = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        LocalDate start = LocalDate.parse(startDate, formatter);
        LocalDate end = LocalDate.parse(endDate, formatter);

        LocalDate current = start;
        while (!current.isAfter(end)) {
            dateList.add(current.format(formatter));
            current = current.plusDays(1);
        }

        return dateList;
    }

    /**
     * 创建Excel文件
     */
    private void createExcelFile(Map<String, Map<String, Map<String, String>>> groupedData,
                                List<String> dateList,
                                HttpServletResponse response) throws IOException {

        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("工作流分析报告");

        // 创建样式
        CellStyle headerStyle = createHeaderStyle(workbook);
        CellStyle dataStyle = createDataStyle(workbook);

        int rowIndex = 0;

        // 创建表头
        Row headerRow = sheet.createRow(rowIndex++);
        Cell cell0 = headerRow.createCell(0);
        cell0.setCellValue("子部门名称");
        cell0.setCellStyle(headerStyle);

        Cell cell1 = headerRow.createCell(1);
        cell1.setCellValue("姓名");
        cell1.setCellStyle(headerStyle);

        // 创建日期列
        for (int i = 0; i < dateList.size(); i++) {
            Cell dateCell = headerRow.createCell(i + 2);
            dateCell.setCellValue(dateList.get(i));
            dateCell.setCellStyle(headerStyle);
        }

        // 填充数据
        for (Map.Entry<String, Map<String, Map<String, String>>> deptEntry : groupedData.entrySet()) {
            String departmentName = deptEntry.getKey();
            Map<String, Map<String, String>> employeeData = deptEntry.getValue();

            for (Map.Entry<String, Map<String, String>> empEntry : employeeData.entrySet()) {
                String employeeName = empEntry.getKey();
                Map<String, String> dateShiftData = empEntry.getValue();

                Row dataRow = sheet.createRow(rowIndex++);

                // 部门名称
                Cell deptCell = dataRow.createCell(0);
                deptCell.setCellValue(departmentName);
                deptCell.setCellStyle(dataStyle);

                // 员工姓名
                Cell empCell = dataRow.createCell(1);
                empCell.setCellValue(employeeName);
                empCell.setCellStyle(dataStyle);

                // 填充日期对应的班次数据
                for (int i = 0; i < dateList.size(); i++) {
                    String date = dateList.get(i);
                    String shiftType = dateShiftData.getOrDefault(date, "");

                    Cell shiftCell = dataRow.createCell(i + 2);
                    shiftCell.setCellValue(shiftType);
                    shiftCell.setCellStyle(dataStyle);
                }
            }
        }

        // 自动调整列宽
        for (int i = 0; i < dateList.size() + 2; i++) {
            sheet.autoSizeColumn(i);
        }

        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=workflow_analysis_report.xlsx");

        // 写入响应
        workbook.write(response.getOutputStream());
        workbook.close();
    }

    /**
     * 创建表头样式
     */
    private CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        Font font = workbook.createFont();
        font.setBold(true);
        style.setFont(font);

        return style;
    }

    /**
     * 创建数据样式
     */
    private CellStyle createDataStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        return style;
    }

}
