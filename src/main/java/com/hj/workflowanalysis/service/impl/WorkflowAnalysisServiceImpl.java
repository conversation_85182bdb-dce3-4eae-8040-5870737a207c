package com.hj.workflowanalysis.service.impl;

import com.hj.workflowanalysis.dao.FormtableMain413Dao;
import com.hj.workflowanalysis.dao.HrmdepartmentDao;
import com.hj.workflowanalysis.dao.HrmresourceDao;
import com.hj.workflowanalysis.dao.WorkflowRequestflowinfoDao;
import com.hj.workflowanalysis.entity.FormtableMain413;
import com.hj.workflowanalysis.entity.Hrmdepartment;
import com.hj.workflowanalysis.entity.Hrmresource;
import com.hj.workflowanalysis.entity.WorkflowRequestflowinfo;
import com.hj.workflowanalysis.service.WorkflowAnalysisService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;


@Service("workflowAnalysisService")
public class WorkflowAnalysisServiceImpl implements WorkflowAnalysisService {

    @Resource
    private WorkflowRequestflowinfoDao workflowRequestflowinfoDao;

    @Resource
    private FormtableMain413Dao formtableMain413Dao;

    @Resource
    private HrmresourceDao hrmresourceDao;

    @Resource
    private HrmdepartmentDao hrmdepartmentDao;

    @Override
    public void analysis() {
        // 1. 获取工作流数据
        List<FormtableMain413> formtableMain413List = formtableMain413Dao.queryAllByLimit("2025-05-01", "2025-05-31");
        List<Integer> requestIds = formtableMain413List.stream().map(FormtableMain413::getRequestid).toList();
        if (CollectionUtils.isEmpty(requestIds)) {
            return;
        }

        List<WorkflowRequestflowinfo> workflowRequestflowinfoList = workflowRequestflowinfoDao.queryAllByLimit(requestIds);
        if (CollectionUtils.isEmpty(workflowRequestflowinfoList)) {
            return;
        }

        // 2. 获取所有操作员ID
        List<Integer> operatorIds = workflowRequestflowinfoList.stream()
                .map(WorkflowRequestflowinfo::getOperator)
                .filter(Objects::nonNull)
                .distinct()
                .toList();

        if (CollectionUtils.isEmpty(operatorIds)) {
            return;
        }

        // 3. 查询员工信息
        List<Hrmresource> hrmresourceList = hrmresourceDao.findByResourceIds(operatorIds);
        Map<Integer, Hrmresource> resourceMap = hrmresourceList.stream()
                .collect(Collectors.toMap(Hrmresource::getId, resource -> resource));

        // 4. 查询所有部门信息
        List<Hrmdepartment> allDepartments = hrmdepartmentDao.queryAll();
        Map<Integer, Hrmdepartment> departmentMap = allDepartments.stream()
                .collect(Collectors.toMap(Hrmdepartment::getId, dept -> dept));

        // 5. 第一步分组：根据员工的departmentid分组
        Map<Integer, List<WorkflowRequestflowinfo>> firstLevelGrouping = workflowRequestflowinfoList.stream()
                .filter(workflow -> workflow.getOperator() != null)
                .filter(workflow -> resourceMap.containsKey(workflow.getOperator()))
                .collect(Collectors.groupingBy(workflow -> {
                    Hrmresource resource = resourceMap.get(workflow.getOperator());
                    return resource.getDepartmentid();
                }));

        // 6. 第二步分组：根据二级部门分组
        Map<Integer, List<WorkflowRequestflowinfo>> secondLevelGrouping = new HashMap<>();

        for (Map.Entry<Integer, List<WorkflowRequestflowinfo>> entry : firstLevelGrouping.entrySet()) {
            Integer departmentId = entry.getKey();
            List<WorkflowRequestflowinfo> workflows = entry.getValue();

            // 找到该部门对应的二级部门
            Integer secondLevelDeptId = findSecondLevelDepartment(departmentId, departmentMap);

            secondLevelGrouping.computeIfAbsent(secondLevelDeptId, k -> new ArrayList<>()).addAll(workflows);
        }

        // 7. 输出结果
        printAnalysisResult(secondLevelGrouping, departmentMap);
    }

    /**
     * 找到指定部门的二级部门ID
     * 二级部门是指从根部门（supdepid为null或0）开始数的第二层部门
     *
     * @param departmentId 部门ID
     * @param departmentMap 部门映射
     * @return 二级部门ID
     */
    private Integer findSecondLevelDepartment(Integer departmentId, Map<Integer, Hrmdepartment> departmentMap) {
        if (departmentId == null || !departmentMap.containsKey(departmentId)) {
            return departmentId; // 如果找不到部门，返回原部门ID
        }

        // 构建从当前部门到根部门的完整路径
        List<Integer> pathToRoot = new ArrayList<>();
        Integer currentId = departmentId;
        Set<Integer> visited = new HashSet<>(); // 防止循环引用

        while (currentId != null && departmentMap.containsKey(currentId) && !visited.contains(currentId)) {
            visited.add(currentId);
            pathToRoot.add(currentId);
            Hrmdepartment dept = departmentMap.get(currentId);
            currentId = dept.getSupdepid();

            // 如果supdepid为0，也视为根部门
            if (currentId != null && currentId == 0) {
                currentId = null;
            }
        }

        // 反转路径，使其从根部门开始
        Collections.reverse(pathToRoot);

        // 如果路径长度小于2，说明没有二级部门，返回当前部门
        if (pathToRoot.size() < 2) {
            return departmentId;
        }

        // 返回第二级部门（索引为1）
        return pathToRoot.get(1);
    }

    /**
     * 打印分析结果
     */
    private void printAnalysisResult(Map<Integer, List<WorkflowRequestflowinfo>> grouping,
                                   Map<Integer, Hrmdepartment> departmentMap) {
        System.out.println("=== 工作流分析结果（按二级部门分组） ===");
        System.out.println("说明：二级部门是指从根部门开始数的第二层部门");
        System.out.println();

        for (Map.Entry<Integer, List<WorkflowRequestflowinfo>> entry : grouping.entrySet()) {
            Integer deptId = entry.getKey();
            List<WorkflowRequestflowinfo> workflows = entry.getValue();

            String deptName = departmentMap.containsKey(deptId) ?
                departmentMap.get(deptId).getDepartmentname() : "未知部门";

            // 显示部门层级路径
            String deptPath = getDepartmentPath(deptId, departmentMap);

            System.out.println(String.format("二级部门: %s (ID: %d)", deptName, deptId));
            System.out.println(String.format("部门路径: %s", deptPath));
            System.out.println(String.format("工作流数量: %d", workflows.size()));

            // 可以根据需要添加更详细的统计信息
            workflows.forEach(workflow -> {
                System.out.println(String.format("  - RequestID: %d, Operator: %d, NodeID: %d",
                    workflow.getRequestid(), workflow.getOperator(), workflow.getNodeid()));
            });
            System.out.println("----------------------------------------");
        }
    }

    /**
     * 获取部门的完整路径
     */
    private String getDepartmentPath(Integer departmentId, Map<Integer, Hrmdepartment> departmentMap) {
        if (departmentId == null || !departmentMap.containsKey(departmentId)) {
            return "未知路径";
        }

        List<String> pathNames = new ArrayList<>();
        Integer currentId = departmentId;
        Set<Integer> visited = new HashSet<>();

        while (currentId != null && departmentMap.containsKey(currentId) && !visited.contains(currentId)) {
            visited.add(currentId);
            Hrmdepartment dept = departmentMap.get(currentId);
            pathNames.add(dept.getDepartmentname());
            currentId = dept.getSupdepid();

            if (currentId != null && currentId == 0) {
                currentId = null;
            }
        }

        Collections.reverse(pathNames);
        return String.join(" > ", pathNames);
    }

}
