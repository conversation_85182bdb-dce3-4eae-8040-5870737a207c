package com.hj.workflowanalysis.service;

import com.hj.workflowanalysis.entity.Hrmdepartment;
import com.hj.workflowanalysis.entity.Hrmresource;
import org.springframework.stereotype.Service;

import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 模拟邮件服务 - 用于测试Excel生成功能
 * 将Excel文件保存到本地而不是发送邮件
 */
@Service
public class MockEmailService {

    private static final String OUTPUT_DIR = "excel_output";

    /**
     * 模拟发送邮件 - 实际上是保存Excel文件到本地
     */
    public void mockSendEmailWithExcel(Hrmresource manager, 
                                     Hrmdepartment department, 
                                     byte[] excelData, 
                                     String startDate, 
                                     String endDate) {
        try {
            // 创建输出目录
            Path outputPath = Paths.get(OUTPUT_DIR);
            if (!Files.exists(outputPath)) {
                Files.createDirectories(outputPath);
            }

            // 生成文件名
            String fileName = String.format("%s_%s_工作流分析报告_%s_%s.xlsx", 
                department.getDepartmentname(),
                manager.getLastname(),
                startDate, 
                endDate);

            // 保存文件
            Path filePath = outputPath.resolve(fileName);
            try (FileOutputStream fos = new FileOutputStream(filePath.toFile())) {
                fos.write(excelData);
            }

            // 模拟邮件发送日志
            System.out.println("=== 模拟邮件发送 ===");
            System.out.println("收件人: " + manager.getLastname() + " (" + manager.getEmail() + ")");
            System.out.println("部门: " + department.getDepartmentname());
            System.out.println("主题: " + department.getDepartmentname() + "工作流分析报告 (" + startDate + " 至 " + endDate + ")");
            System.out.println("Excel文件已保存到: " + filePath.toAbsolutePath());
            System.out.println("文件大小: " + (excelData.length / 1024) + " KB");
            System.out.println("==================");

        } catch (IOException e) {
            System.err.println("保存Excel文件失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 获取输出目录路径
     */
    public String getOutputDirectory() {
        return Paths.get(OUTPUT_DIR).toAbsolutePath().toString();
    }
}
