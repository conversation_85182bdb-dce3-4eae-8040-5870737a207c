package com.hj.workflowanalysis.dao;

import com.hj.workflowanalysis.entity.Hrmresource;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface HrmresourceDao {

    /**
     * 根据部门ID查询员工
     *
     * @param departmentId 部门ID
     * @return 员工列表
     */
    List<Hrmresource> findByDepartmentId(@Param("departmentId") Integer departmentId);

    /**
     * 根据员工ID列表查询员工信息
     *
     * @param resourceIds 员工ID列表
     * @return 员工列表
     */
    List<Hrmresource> findByResourceIds(@Param("resourceIds") List<Integer> resourceIds);

    /**
     * 根据部门ID查询部门经理（seclevel > 10）
     *
     * @param departmentId 部门ID
     * @return 部门经理列表
     */
    List<Hrmresource> findManagersByDepartmentId(@Param("departmentId") Integer departmentId);

}

