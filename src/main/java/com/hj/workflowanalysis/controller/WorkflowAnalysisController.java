package com.hj.workflowanalysis.controller;

import com.hj.workflowanalysis.service.WorkflowAnalysisService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/workflowAnalysis")
public class WorkflowAnalysisController {

    @Resource
    private WorkflowAnalysisService workflowAnalysisService;

    @RequestMapping("/analysis")
    public String analysis() {
        workflowAnalysisService.analysis();
        return "success";
    }

    /**
     * 导出Excel格式的工作流分析报告
     *
     * @param startDate 开始日期，格式：yyyy-MM-dd
     * @param endDate 结束日期，格式：yyyy-MM-dd
     * @param response HTTP响应
     */
    @GetMapping("/exportExcel")
    public void exportExcel(@RequestParam(defaultValue = "2025-05-01") String startDate,
                           @RequestParam(defaultValue = "2025-05-31") String endDate,
                           HttpServletResponse response) {
        workflowAnalysisService.exportExcel(startDate, endDate, response);
    }

    /**
     * 按二级部门分别导出Excel并发送邮件给部门经理
     *
     * @param startDate 开始日期，格式：yyyy-MM-dd
     * @param endDate 结束日期，格式：yyyy-MM-dd
     * @return 操作结果
     */
    @GetMapping("/exportAndSendEmail")
    public String exportAndSendEmailByDepartment(@RequestParam(defaultValue = "2025-05-01") String startDate,
                                                @RequestParam(defaultValue = "2025-05-31") String endDate) {
        try {
            workflowAnalysisService.exportAndSendEmailByDepartment(startDate, endDate);
            return "Excel文件已按部门生成并发送邮件给相应经理";
        } catch (Exception e) {
            return "操作失败: " + e.getMessage();
        }
    }

}
