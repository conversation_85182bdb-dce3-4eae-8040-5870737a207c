package com.hj.workflowanalysis.util;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Component;

@Component
public class EmailTestUtil {

    @Autowired
    private JavaMailSender mailSender;

    @Value("${spring.mail.username}")
    private String fromEmail;

    /**
     * 测试邮件发送功能
     */
    public void testEmailConnection() {
        try {
            System.out.println("开始测试邮件连接...");
            System.out.println("发送方邮箱: " + fromEmail);
            
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(fromEmail);
            message.setTo(fromEmail); // 发送给自己进行测试
            message.setSubject("邮件连接测试");
            message.setText("这是一封测试邮件，用于验证邮件服务器连接是否正常。");
            
            mailSender.send(message);
            System.out.println("邮件发送成功！");
            
        } catch (Exception e) {
            System.err.println("邮件发送失败: " + e.getMessage());
            e.printStackTrace();
            
            // 提供详细的错误诊断
            System.err.println("\n=== 邮件配置诊断 ===");
            System.err.println("1. 请检查QQ邮箱是否开启了SMTP服务");
            System.err.println("2. 请确认授权码是否正确（不是QQ密码）");
            System.err.println("3. 请检查网络连接和防火墙设置");
            System.err.println("4. 如果在公司网络，可能需要配置代理");
        }
    }

    /**
     * 测试网络连接
     */
    public void testNetworkConnection() {
        try {
            System.out.println("测试网络连接到 smtp.qq.com:587...");
            
            java.net.Socket socket = new java.net.Socket();
            socket.connect(new java.net.InetSocketAddress("smtp.qq.com", 587), 5000);
            socket.close();
            
            System.out.println("网络连接正常！");
            
        } catch (Exception e) {
            System.err.println("网络连接失败: " + e.getMessage());
            System.err.println("可能的原因：");
            System.err.println("1. 防火墙阻止了连接");
            System.err.println("2. 公司网络限制了SMTP端口");
            System.err.println("3. 需要配置网络代理");
        }
    }
}
