server:
  port: 8103
spring:
  application:
    name: workflow-analysis
  # 多数据源配置
  datasource:
    driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
    url: *************************************************************************************************
    username: sa
    password: Hj@20201001
  # Quartz配置
  quartz:
    job-store-type: memory # 使用内存存储任务信息
    scheduler-name: LaborCostScheduler
    auto-startup: true # 自动启动调度器
    startup-delay: 5s # 延迟5秒启动调度器
    wait-for-jobs-to-complete-on-shutdown: true # 关闭时等待任务完成
    overwrite-existing-jobs: true # 覆盖已存在的任务
    properties:
      org.quartz.threadPool.threadCount: 5 # 线程池大小
mybatis:
  mapper-locations: classpath*:mapper/*.xml
  type-aliases-package: com.hj.workflowanalysis.entity