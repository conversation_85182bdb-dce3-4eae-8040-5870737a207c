<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hj.workflowanalysis.dao.HrmresourceDao">

    <resultMap type="com.hj.workflowanalysis.entity.Hrmresource" id="HrmresourceMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="lastname" column="lastname" jdbcType="VARCHAR"/>
        <result property="seclevel" column="seclevel" jdbcType="INTEGER"/>
        <result property="departmentid" column="departmentid" jdbcType="INTEGER"/>
        <result property="usekind" column="usekind" jdbcType="INTEGER"/>
    </resultMap>

    <!--根据部门ID查询员工-->
    <select id="findByDepartmentId" resultMap="HrmresourceMap">
        SELECT
            id, lastname, seclevel, departmentid, usekind
        FROM [ecology].[dbo].[HrmResource] WITH(NOLOCK)
        WHERE departmentid = #{departmentId}
    </select>

    <!--根据员工ID列表查询员工信息-->
    <select id="findByResourceIds" resultMap="HrmresourceMap">
        SELECT
            id, lastname, seclevel, departmentid, usekind
        FROM [ecology].[dbo].[HrmResource] WITH(NOLOCK)
        <where>
            <if test="resourceIds != null and resourceIds.size() > 0">
                id in
                <foreach item="resourceId" index="index" collection="resourceIds" open="(" separator="," close=")">
                    #{resourceId}
                </foreach>
            </if>
        </where>
    </select>

</mapper>

