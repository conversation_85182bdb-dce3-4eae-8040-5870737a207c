<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hj.workflowanalysis.dao.WorkflowRequestflowinfoDao">

    <select id="queryAllByLimit" resultType="com.hj.workflowanalysis.entity.WorkflowRequestflowinfo">
        select *
        from workflow_requestflowinfo
        where requestid in
        <foreach item="requestId" index="index" collection="requestIds" open="(" separator="," close=")">
            #{requestId}
        </foreach>
    </select>

</mapper>

