<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hj.workflowanalysis.dao.WorkflowRequestflowinfoDao">

<!--    <resultMap type="com.hj.workflowanalysis.entity.WorkflowRequestflowinfo" id="WorkflowRequestflowinfoMap">-->
<!--        <result property="id" column="id" jdbcType="INTEGER"/>-->
<!--        <result property="requestid" column="requestid" jdbcType="INTEGER"/>-->
<!--        <result property="workflowid" column="workflowid" jdbcType="INTEGER"/>-->
<!--        <result property="operator" column="operator" jdbcType="INTEGER"/>-->
<!--        <result property="nodeid" column="nodeid" jdbcType="INTEGER"/>-->
<!--        <result property="destnodeid" column="destnodeid" jdbcType="INTEGER"/>-->
<!--        <result property="logtype" column="logtype" jdbcType="VARCHAR"/>-->
<!--        <result property="loguuid" column="loguuid" jdbcType="VARCHAR"/>-->
<!--        <result property="currentoperatoruuid" column="currentoperatoruuid" jdbcType="VARCHAR"/>-->
<!--        <result property="operatedate" column="operatedate" jdbcType="VARCHAR"/>-->
<!--        <result property="operatetime" column="operatetime" jdbcType="VARCHAR"/>-->
<!--        <result property="rejectnodeids" column="rejectnodeids" jdbcType="VARCHAR"/>-->
<!--        <result property="rejectrangenodes" column="rejectRangeNodes" jdbcType="VARCHAR"/>-->
<!--        <result property="issubmittorejectnode" column="isSubmitToRejectNode" jdbcType="VARCHAR"/>-->
<!--        <result property="mergenodeids" column="mergeNodeIds" jdbcType="VARCHAR"/>-->
<!--        <result property="linkid" column="linkid" jdbcType="INTEGER"/>-->
<!--    </resultMap>-->

    <!--查询指定行数据-->
<!--    <select id="queryAllByLimit" resultMap="WorkflowRequestflowinfoMap">-->
    <select id="queryAllByLimit" resultType="com.hj.workflowanalysis.entity.WorkflowRequestflowinfo">
        select *
<!--id, requestid, workflowid, operator, nodeid, destnodeid, logtype, loguuid, currentoperatoruuid, operatedate, operatetime, rejectnodeids, rejectRangeNodes, isSubmitToRejectNode, mergeNodeIds, linkid-->
        from workflow_requestflowinfo
        <where>
            <if test="requestIds != null and requestIds.size() &gt; 0">
                and requestid in
                <foreach item="requestId" index="index" collection="requestIds" open="(" separator="," close=")">
                    #{requestId}
                </foreach>
            </if>
        </where>
    </select>

</mapper>

